package com.sankuai.meituan.waimai.customer.service.sign.wrapcontext;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil;
import com.sankuai.meituan.waimai.econtrct.client.constants.EcontractApplySubDataEnum;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.EcontractApplySubDataBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.StageBatchInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractBatchTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.*;
import com.sankuai.shangou.merchant.logistics.thrift.constants.LogisticsFeeModeEnum;
import org.apache.commons.collections.MapUtils;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;
import com.sankuai.meituan.waimai.customer.bo.sign.EcontractBatchMiddleBo;
import com.sankuai.meituan.waimai.customer.constant.sign.WmSignConstant;
import com.sankuai.meituan.waimai.econtrct.client.constants.EcontractRecordSourceEnum;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.EcontractBatchBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @description: 电子签约数据来源
 * @author: lixuepeng
 * @create: 2023-06-2
 **/
@Service
@Slf4j
public class WmEcontractDataApplySubDataCollector implements IWmEcontractDataCollector {

    @Override
    public void collect(EcontractBatchContextBo originContext, EcontractBatchMiddleBo middleContext, EcontractBatchBo targetContext)
            throws WmCustomerException, IllegalAccessException, TException {
        log.info("WmEcontractDataApplySubDataCollector#batchId:{}, batchTypeEnum:{}", originContext.getBatchId(), originContext.getBatchTypeEnum());
        EcontractSignDataFactor signDataFactor = middleContext.getSignDataFactor();
        List<EcontractApplySubDataBo> applySubDataBoList = new ArrayList<>();
        if (EcontractBatchTypeEnum.NATIONAL_SUBSIDY_HEADQUARTERS_DELIVERY == originContext.getBatchTypeEnum()) {
            collectHeadQuartesDelivery(originContext, applySubDataBoList, signDataFactor);
            return;
        } else if (EcontractBatchTypeEnum.NATIONAL_SUBSIDY_DISTRIBUTOR_DELIVERY == originContext.getBatchTypeEnum()) {
            collectDistributorDelivery(originContext, applySubDataBoList, signDataFactor);
            return;
        }
        if (signDataFactor.isDeliverySingleWmPoi()) {
            EcontractTaskBo taskBo = WmEcontractContextUtil.selectByApplyType(originContext, EcontractTaskApplyTypeEnum.POIFEE);
            EcontractDeliveryInfoBo deliveryInfoBo = JSON.parseObject(taskBo.getApplyContext(), EcontractDeliveryInfoBo.class);
            if ("true".equals(deliveryInfoBo.getHasExternalPerInfo())) {
                EcontractApplySubDataBo subDataBo = new EcontractApplySubDataBo();
                Map<Long, Long> wmPoiAndBizMap = new HashMap<>();
                if (isSupportSGOneChainAndSGFeeModeAndMTDelivery(deliveryInfoBo)) {
                    subDataBo.setApplySubDataEnum(EcontractApplySubDataEnum.SG_2_DELIVERY_SINGLE_PERFORMANCE_PDF);
                    wmPoiAndBizMap.put(Long.valueOf(deliveryInfoBo.getWmPoiId()), Long.valueOf(deliveryInfoBo.getExternalPerInfoBizId()));
                } else {
                    subDataBo.setApplySubDataEnum(EcontractApplySubDataEnum.DELIVERY_SINGLE_PERFORMANCE_PDF);
                    wmPoiAndBizMap.put(Long.valueOf(deliveryInfoBo.getWmPoiId()), Long.valueOf(deliveryInfoBo.getExternalPerInfoBizId()));
                }
                subDataBo.setWmPoiAndBizMap(wmPoiAndBizMap);
                applySubDataBoList.add(subDataBo);
            }
            if ("true".equals(deliveryInfoBo.getHasExternalAreaInfo())) {
                EcontractApplySubDataBo subDataBo = new EcontractApplySubDataBo();
                subDataBo.setApplySubDataEnum(EcontractApplySubDataEnum.DELIVERY_SINGLE_AREA);
                Map<Long, Long> wmPoiAndBizMap = new HashMap<>();
                wmPoiAndBizMap.put(Long.valueOf(deliveryInfoBo.getWmPoiId()), Long.valueOf(deliveryInfoBo.getExternalAreaInfoBizId()));
                subDataBo.setWmPoiAndBizMap(wmPoiAndBizMap);
                applySubDataBoList.add(subDataBo);
            }
        } else if (signDataFactor.isDeliveryMultiWmPoi()) {
            EcontractTaskBo taskBo = WmEcontractContextUtil.selectByApplyType(originContext, EcontractTaskApplyTypeEnum.BATCHPOIFEE);
            EcontractBatchDeliveryInfoBo batchDeliveryInfoBo = JSON.parseObject(taskBo.getApplyContext(), EcontractBatchDeliveryInfoBo.class);
            EcontractApplySubDataBo multiPerSubDataBo = new EcontractApplySubDataBo();
            multiPerSubDataBo.setApplySubDataEnum(EcontractApplySubDataEnum.DELIVERY_MULTI_PERFORMANCE_PDF);
            multiPerSubDataBo.setWmPoiAndBizMap(new HashMap<>());
            EcontractApplySubDataBo mulAreaSubDataBo = new EcontractApplySubDataBo();
            mulAreaSubDataBo.setApplySubDataEnum(EcontractApplySubDataEnum.DELIVERY_MULTI_AREA);
            mulAreaSubDataBo.setWmPoiAndBizMap(new HashMap<>());

            EcontractApplySubDataBo sgMultiPerSubDataBo = new EcontractApplySubDataBo();
            sgMultiPerSubDataBo.setApplySubDataEnum(EcontractApplySubDataEnum.SG_2_DELIVERY_MULTI_PERFORMANCE_PDF);
            sgMultiPerSubDataBo.setWmPoiAndBizMap(new HashMap<>());
            for (EcontractDeliveryInfoBo deliveryInfoBo : batchDeliveryInfoBo.getEcontractDeliveryInfoBoList()) {
                if ("true".equals(deliveryInfoBo.getHasExternalPerInfo())) {
                    if (isSupportSGOneChainAndSGFeeModeAndMTDelivery(deliveryInfoBo)) {
                        sgMultiPerSubDataBo.getWmPoiAndBizMap().put(Long.valueOf(deliveryInfoBo.getWmPoiId()), Long.valueOf(deliveryInfoBo.getExternalPerInfoBizId()));
                    } else {
                        multiPerSubDataBo.getWmPoiAndBizMap().put(Long.valueOf(deliveryInfoBo.getWmPoiId()), Long.valueOf(deliveryInfoBo.getExternalPerInfoBizId()));
                    }
                }
                if ("true".equals(deliveryInfoBo.getHasExternalAreaInfo())) {
                    mulAreaSubDataBo.getWmPoiAndBizMap().put(Long.valueOf(deliveryInfoBo.getWmPoiId()), Long.valueOf(deliveryInfoBo.getExternalAreaInfoBizId()));
                }
            }
            if (MapUtils.isNotEmpty(multiPerSubDataBo.getWmPoiAndBizMap())) {
                applySubDataBoList.add(multiPerSubDataBo);
            }
            if (MapUtils.isNotEmpty(mulAreaSubDataBo.getWmPoiAndBizMap())) {
                applySubDataBoList.add(mulAreaSubDataBo);
            }
            if (MapUtils.isNotEmpty(sgMultiPerSubDataBo.getWmPoiAndBizMap())) {
                applySubDataBoList.add(sgMultiPerSubDataBo);
            }
        }
        targetContext.setApplySubDataBoList(applySubDataBoList);
    }

    private void collectDistributorDelivery(EcontractBatchContextBo originContext,
                                            List<EcontractApplySubDataBo> applySubDataBoList, EcontractSignDataFactor signDataFactor) throws WmCustomerException {
        if (signDataFactor.isDeliverySingleWmPoi()) {
            EcontractTaskBo taskBo = WmEcontractContextUtil.selectByApplyType(originContext, EcontractTaskApplyTypeEnum.NATIONAL_SUBSIDY_DISTRIBUTOR_DELIVERY);
            EcontractDeliveryInfoBo deliveryInfoBo = JSON.parseObject(taskBo.getApplyContext(), EcontractDeliveryInfoBo.class);
            if (hasExternalPerInfo(deliveryInfoBo)) {
                EcontractApplySubDataBo subDataBo = new EcontractApplySubDataBo();
                Map<Long, Long> wmPoiAndBizMap = new HashMap<>();
                subDataBo.setApplySubDataEnum(EcontractApplySubDataEnum.NATIONAL_SUBSIDY_DISTRIBUTOR_SINGLE_DELIVERY_PERFORMANCE_SERVICE_FEE);
                wmPoiAndBizMap.put(Long.valueOf(deliveryInfoBo.getWmPoiId()), Long.valueOf(deliveryInfoBo.getExternalPerInfoBizId()));
                subDataBo.setWmPoiAndBizMap(wmPoiAndBizMap);
                applySubDataBoList.add(subDataBo);
            }
            if (hasExternalAreaInfo(deliveryInfoBo)) {
                EcontractApplySubDataBo subDataBo = new EcontractApplySubDataBo();
                subDataBo.setApplySubDataEnum(EcontractApplySubDataEnum.NATIONAL_SUBSIDY_DISTRIBUTOR_SINGLE_AREA);
                Map<Long, Long> wmPoiAndBizMap = new HashMap<>();
                wmPoiAndBizMap.put(Long.valueOf(deliveryInfoBo.getWmPoiId()), Long.valueOf(deliveryInfoBo.getExternalAreaInfoBizId()));
                subDataBo.setWmPoiAndBizMap(wmPoiAndBizMap);
                applySubDataBoList.add(subDataBo);
            }
        } else if (signDataFactor.isDeliveryMultiWmPoi()) {
            EcontractTaskBo taskBo = WmEcontractContextUtil.selectByApplyType(originContext, EcontractTaskApplyTypeEnum.NATIONAL_SUBSIDY_DISTRIBUTOR_DELIVERY);
            EcontractBatchDeliveryInfoBo batchDeliveryInfoBo = JSON.parseObject(taskBo.getApplyContext(), EcontractBatchDeliveryInfoBo.class);
            EcontractApplySubDataBo multiPerSubDataBo = new EcontractApplySubDataBo();
            multiPerSubDataBo.setApplySubDataEnum(EcontractApplySubDataEnum.NATIONAL_SUBSIDY_DISTRIBUTOR_MULTI_DELIVERY_PERFORMANCE_SERVICE_FEE);
            multiPerSubDataBo.setWmPoiAndBizMap(new HashMap<>());
            EcontractApplySubDataBo mulAreaSubDataBo = new EcontractApplySubDataBo();
            mulAreaSubDataBo.setApplySubDataEnum(EcontractApplySubDataEnum.NATIONAL_SUBSIDY_DISTRIBUTOR_MULTI_AREA);
            mulAreaSubDataBo.setWmPoiAndBizMap(new HashMap<>());

            for (EcontractDeliveryInfoBo deliveryInfoBo : batchDeliveryInfoBo.getEcontractDeliveryInfoBoList()) {
                if (hasExternalPerInfo(deliveryInfoBo)) {
                    multiPerSubDataBo.getWmPoiAndBizMap().put(Long.valueOf(deliveryInfoBo.getWmPoiId()), Long.valueOf(deliveryInfoBo.getExternalPerInfoBizId()));
                }
                if (hasExternalAreaInfo(deliveryInfoBo)) {
                    mulAreaSubDataBo.getWmPoiAndBizMap().put(Long.valueOf(deliveryInfoBo.getWmPoiId()), Long.valueOf(deliveryInfoBo.getExternalAreaInfoBizId()));
                }
            }
            if (MapUtils.isNotEmpty(multiPerSubDataBo.getWmPoiAndBizMap())) {
                applySubDataBoList.add(multiPerSubDataBo);
            }
            if (MapUtils.isNotEmpty(mulAreaSubDataBo.getWmPoiAndBizMap())) {
                applySubDataBoList.add(mulAreaSubDataBo);
            }
        }
    }

    private void collectHeadQuartesDelivery(EcontractBatchContextBo originContext,
                                            List<EcontractApplySubDataBo> applySubDataBoList,
                                            EcontractSignDataFactor signDataFactor) throws WmCustomerException {
        if (signDataFactor.isDeliverySingleWmPoi()) {
            EcontractTaskBo taskBo = WmEcontractContextUtil.selectByApplyType(originContext, EcontractTaskApplyTypeEnum.NATIONAL_SUBSIDY_HEADQUARTERS_DELIVERY);
            EcontractDeliveryInfoBo deliveryInfoBo = JSON.parseObject(taskBo.getApplyContext(), EcontractDeliveryInfoBo.class);
            if (hasExternalPerInfo(deliveryInfoBo)) {
                EcontractApplySubDataBo subDataBo = new EcontractApplySubDataBo();
                Map<Long, Long> wmPoiAndBizMap = new HashMap<>();
                subDataBo.setApplySubDataEnum(EcontractApplySubDataEnum.NATIONAL_SUBSIDY_HEADQUARTERS_SINGLE_DELIVERY_PERFORMANCE_SERVICE_FEE);
                wmPoiAndBizMap.put(Long.valueOf(deliveryInfoBo.getWmPoiId()), Long.valueOf(deliveryInfoBo.getExternalPerInfoBizId()));
                subDataBo.setWmPoiAndBizMap(wmPoiAndBizMap);
                applySubDataBoList.add(subDataBo);
            }
            if (hasExternalAreaInfo(deliveryInfoBo)) {
                EcontractApplySubDataBo subDataBo = new EcontractApplySubDataBo();
                subDataBo.setApplySubDataEnum(EcontractApplySubDataEnum.NATIONAL_SUBSIDY_HEADQUARTERS_SINGLE_AREA);
                Map<Long, Long> wmPoiAndBizMap = new HashMap<>();
                wmPoiAndBizMap.put(Long.valueOf(deliveryInfoBo.getWmPoiId()), Long.valueOf(deliveryInfoBo.getExternalAreaInfoBizId()));
                subDataBo.setWmPoiAndBizMap(wmPoiAndBizMap);
                applySubDataBoList.add(subDataBo);
            }
        } else if (signDataFactor.isDeliveryMultiWmPoi()) {
            EcontractTaskBo taskBo = WmEcontractContextUtil.selectByApplyType(originContext, EcontractTaskApplyTypeEnum.NATIONAL_SUBSIDY_HEADQUARTERS_DELIVERY);
            EcontractBatchDeliveryInfoBo batchDeliveryInfoBo = JSON.parseObject(taskBo.getApplyContext(), EcontractBatchDeliveryInfoBo.class);
            EcontractApplySubDataBo multiPerSubDataBo = new EcontractApplySubDataBo();
            multiPerSubDataBo.setApplySubDataEnum(EcontractApplySubDataEnum.NATIONAL_SUBSIDY_HEADQUARTERS_MULTI_DELIVERY_PERFORMANCE_SERVICE_FEE);
            multiPerSubDataBo.setWmPoiAndBizMap(new HashMap<>());
            EcontractApplySubDataBo mulAreaSubDataBo = new EcontractApplySubDataBo();
            mulAreaSubDataBo.setApplySubDataEnum(EcontractApplySubDataEnum.NATIONAL_SUBSIDY_HEADQUARTERS_MULTI_AREA);
            mulAreaSubDataBo.setWmPoiAndBizMap(new HashMap<>());

            for (EcontractDeliveryInfoBo deliveryInfoBo : batchDeliveryInfoBo.getEcontractDeliveryInfoBoList()) {
                if (hasExternalPerInfo(deliveryInfoBo)) {
                    multiPerSubDataBo.getWmPoiAndBizMap().put(Long.valueOf(deliveryInfoBo.getWmPoiId()), Long.valueOf(deliveryInfoBo.getExternalPerInfoBizId()));
                }
                if (hasExternalAreaInfo(deliveryInfoBo)) {
                    mulAreaSubDataBo.getWmPoiAndBizMap().put(Long.valueOf(deliveryInfoBo.getWmPoiId()), Long.valueOf(deliveryInfoBo.getExternalAreaInfoBizId()));
                }
            }
            if (MapUtils.isNotEmpty(multiPerSubDataBo.getWmPoiAndBizMap())) {
                applySubDataBoList.add(multiPerSubDataBo);
            }
            if (MapUtils.isNotEmpty(mulAreaSubDataBo.getWmPoiAndBizMap())) {
                applySubDataBoList.add(mulAreaSubDataBo);
            }
        }
    }

    private boolean hasExternalPerInfo(EcontractDeliveryInfoBo deliveryInfoBo) {
        return "true".equals(deliveryInfoBo.getHasExternalPerInfo());
    }

    private boolean hasExternalAreaInfo(EcontractDeliveryInfoBo deliveryInfoBo) {
        return "true".equals(deliveryInfoBo.getHasExternalAreaInfo());
    }

    /**
     * 是否是一体化链路的闪购2.0费率门店,且为美团配送
     *
     * @param deliveryInfoBo
     * @return
     */
    private boolean isSupportSGOneChainAndSGFeeModeAndMTDelivery(EcontractDeliveryInfoBo deliveryInfoBo) {
        return String.valueOf(LogisticsFeeModeEnum.SHANGOU.getCode()).equals(deliveryInfoBo.getFeeMode());
    }
}
